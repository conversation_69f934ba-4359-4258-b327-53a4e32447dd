// 吴 - 内容脚本
class AdPopupKiller {
    constructor() {
        this.scanInterval = null;
        this.closedCount = 0;
        this.isDestroyed = false;
        this.retryCount = 0;
        this.maxRetries = 3;
        
        // 支持多种弹窗的关闭按钮选择器
        this.closeButtonSelectors = [
            // 原有的弹窗选择器（5-118-0版本）
            'svg[data-testid="beast-core-modal-icon-close"]',
            '.MDL_headerCloseIcon_5-118-0',
            '.ICN_outerWrapper_5-118-0',
            '.ICN_svgIcon_5-118-0',
            // 新增：第一个特定弹窗选择器
            'svg[data-testid="beast-core-icon-close"]',
            '.modal-content_closeIcon__2C8Fb',
            '.modal-content_closeIcon__Gw9DZ',
            '.modal-content_closeIcon__7mkdd',
            // 新增：第二个特定弹窗选择器（5-119-0版本）
            '.MDL_headerCloseIcon_5-119-0',
            '.ICN_outerWrapper_5-119-0',
            '.ICN_svgIcon_5-119-0',
            // 组合选择器 - 更精准匹配
            'svg[data-testid="beast-core-icon-close"].ICN_outerWrapper_5-119-0',
            'svg[data-testid="beast-core-icon-close"].modal-content_closeIcon__2C8Fb',
            'svg[data-testid="beast-core-icon-close"].modal-content_closeIcon__Gw9DZ',
            'svg[data-testid="beast-core-icon-close"].modal-content_closeIcon__7mkdd',
            'svg[data-testid="beast-core-modal-icon-close"].ICN_outerWrapper_5-119-0',
            'svg[data-testid="beast-core-modal-icon-close"].MDL_headerCloseIcon_5-119-0'
        ];

        // 需要排除的元素选择器（不要点击这些）
        this.excludeSelectors = [
            // 原有版本的排除选择器
            '.MDL_iconWrapper_5-118-0',
            'div.MDL_iconWrapper_5-118-0',
            // 新版本的排除选择器
            '.MDL_iconWrapper_5-119-0',
            'div.MDL_iconWrapper_5-119-0',
            // 通用排除选择器
            '[class*="MDL_iconWrapper"]',
            // 排除可能的父容器，避免误点击
            '[class*="modal-content"]',
            '.modal-content'
        ];
        
        // 特定弹窗容器选择器（更严格的匹配）
        this.popupContainerSelectors = [
            // 只匹配包含特定类名模式的容器
            '[class*="MDL_"]',
            '[class*="modal"]',
            '[role="dialog"]',
            '[role="alertdialog"]'
        ];
        
        this.init();
    }
    
    init() {
        console.log('吴已启动 - 持续监控模式');
        this.startScanning();

        // 监听页面变化，确保持续生效
        this.setupPageChangeListeners();

        // 监听可见性变化，页面重新激活时重启扫描
        this.setupVisibilityChangeListener();
    }
    
    startScanning() {
        if (this.isDestroyed) return;

        if (this.scanInterval) {
            clearInterval(this.scanInterval);
        }

        console.log('开始扫描弹窗 - 每0.5秒检测一次');
        this.scanInterval = setInterval(() => {
            if (this.isDestroyed) {
                this.stopScanning();
                return;
            }
            this.scanAndClosePopups();
        }, 500); // 每0.5秒扫描一次

        // 立即执行一次扫描
        this.scanAndClosePopups();
    }

    stopScanning() {
        if (this.scanInterval) {
            clearInterval(this.scanInterval);
            this.scanInterval = null;
            console.log('停止扫描弹窗');
        }
    }
    
    scanAndClosePopups() {
        try {
            if (this.isDestroyed) return;

            // 查找并关闭弹窗
            const closeButtons = this.findCloseButtons();

            if (closeButtons.length > 0) {
                console.log(`发现 ${closeButtons.length} 个潜在的关闭按钮`);
            }

            closeButtons.forEach(button => {
                if (this.isVisibleAndClickable(button)) {
                    this.clickCloseButton(button);
                }
            });

            // 重置重试计数
            this.retryCount = 0;

        } catch (error) {
            console.error('扫描弹窗时出错:', error);
            this.handleScanError(error);
        }
    }

    handleScanError(error) {
        this.retryCount++;
        console.warn(`扫描出错，重试次数: ${this.retryCount}/${this.maxRetries}`);

        if (this.retryCount >= this.maxRetries) {
            console.error('扫描错误次数过多，重启扫描器');
            this.stopScanning();
            setTimeout(() => {
                if (!this.isDestroyed) {
                    this.retryCount = 0;
                    this.startScanning();
                }
            }, 2000);
        }
    }
    
    findCloseButtons() {
        const buttons = [];

        // 使用特定选择器查找关闭按钮
        this.closeButtonSelectors.forEach(selector => {
            try {
                const elements = document.querySelectorAll(selector);
                elements.forEach(element => {
                    // 额外验证：确保是你提供的特定SVG元素
                    if (this.isTargetCloseButton(element) && !buttons.includes(element)) {
                        buttons.push(element);
                    }
                });
            } catch (e) {
                // 忽略无效的选择器
            }
        });

        return buttons;
    }

    isTargetCloseButton(element) {
        // 首先检查是否是需要排除的元素
        if (this.isExcludedElement(element)) {
            console.log('排除的元素，不点击:', element.className);
            return false;
        }

        // 验证是否是目标关闭按钮
        if (element.tagName === 'svg') {
            // 检查SVG元素的特征
            const testId = element.getAttribute('data-testid');
            const viewBox = element.getAttribute('viewBox');
            const classes = element.className.baseVal || element.className;

            // 检查原有弹窗类型（5-118-0版本）
            const isOriginalPopup = testId === 'beast-core-modal-icon-close' &&
                                   viewBox === '0 0 1024 1024' &&
                                   (classes.includes('ICN_outerWrapper_5-118-0') ||
                                    classes.includes('MDL_headerCloseIcon_5-118-0') ||
                                    classes.includes('ICN_svgIcon_5-118-0'));

            // 检查第一个新弹窗类型
            const isFirstNewPopup = testId === 'beast-core-icon-close' &&
                                   viewBox === '0 0 1024 1024' &&
                                   (classes.includes('ICN_outerWrapper_5-119-0') ||
                                    classes.includes('ICN_svgIcon_5-119-0') ||
                                    classes.includes('modal-content_closeIcon__2C8Fb') ||
                                    classes.includes('modal-content_closeIcon__Gw9DZ') ||
                                    classes.includes('modal-content_closeIcon__7mkdd'));

            // 检查第二个新弹窗类型（beast-core-modal-icon-close但5-119-0版本）
            const isSecondNewPopup = testId === 'beast-core-modal-icon-close' &&
                                    viewBox === '0 0 1024 1024' &&
                                    (classes.includes('ICN_outerWrapper_5-119-0') ||
                                     classes.includes('MDL_headerCloseIcon_5-119-0') ||
                                     classes.includes('ICN_svgIcon_5-119-0'));

            const isValidSvg = isOriginalPopup || isFirstNewPopup || isSecondNewPopup;

            // 额外检查：确保SVG不在排除的容器内
            if (isValidSvg) {
                // 检查是否在原有版本的排除容器内
                const oldParentContainer = element.closest('.MDL_iconWrapper_5-118-0');
                // 检查是否在新版本的排除容器内
                const newParentContainer = element.closest('.MDL_iconWrapper_5-119-0');

                if (oldParentContainer || newParentContainer) {
                    console.log('SVG在排除的容器内，不点击');
                    return false;
                }
                return true;
            }
            return false;
        }

        // 如果是其他元素，检查是否包含目标SVG
        const originalTargetSvg = element.querySelector('svg[data-testid="beast-core-modal-icon-close"]');
        const newTargetSvg = element.querySelector('svg[data-testid="beast-core-icon-close"]');

        if (originalTargetSvg || newTargetSvg) {
            const targetSvg = originalTargetSvg || newTargetSvg;
            // 确保找到的SVG不在排除的容器内
            const oldParentContainer = targetSvg.closest('.MDL_iconWrapper_5-118-0');
            const newParentContainer = targetSvg.closest('.MDL_iconWrapper_5-119-0');

            if (oldParentContainer || newParentContainer) {
                console.log('找到的SVG在排除的容器内，不点击');
                return false;
            }
            return true;
        }

        return false;
    }

    isExcludedElement(element) {
        // 检查元素本身是否匹配排除选择器
        for (const selector of this.excludeSelectors) {
            try {
                if (element.matches && element.matches(selector)) {
                    return true;
                }
            } catch (e) {
                // 忽略选择器错误
            }
        }

        // 检查元素是否在排除的容器内
        for (const selector of this.excludeSelectors) {
            try {
                if (element.closest && element.closest(selector)) {
                    return true;
                }
            } catch (e) {
                // 忽略选择器错误
            }
        }

        return false;
    }
    
    isVisibleAndClickable(element) {
        if (!element) return false;
        
        // 检查元素是否可见
        const style = window.getComputedStyle(element);
        if (style.display === 'none' || 
            style.visibility === 'hidden' || 
            style.opacity === '0') {
            return false;
        }
        
        // 检查元素是否在视口内
        const rect = element.getBoundingClientRect();
        if (rect.width === 0 || rect.height === 0) {
            return false;
        }
        
        // 检查是否在弹窗容器内
        return this.isInPopupContainer(element);
    }
    
    isInPopupContainer(element) {
        // 检查元素是否在特定的弹窗容器内
        let parent = element.parentElement;
        while (parent) {
            const className = parent.className || '';

            // 更严格的检查：必须包含特定的类名模式
            if (className.includes && (
                className.includes('MDL_') ||
                className.includes('modal') ||
                parent.getAttribute('role') === 'dialog' ||
                parent.getAttribute('role') === 'alertdialog'
            )) {
                // 额外检查：确保容器有较高的z-index（弹窗特征）
                const style = window.getComputedStyle(parent);
                const zIndex = parseInt(style.zIndex);
                if (zIndex > 100) {
                    return true;
                }
            }

            parent = parent.parentElement;
        }

        return false;
    }
    
    clickCloseButton(button) {
        try {
            console.log('发现目标弹窗关闭按钮，正在点击...', button);

            // 记录关闭的弹窗信息（用于调试）
            const testId = button.getAttribute('data-testid');
            const classes = button.className.baseVal || button.className;
            console.log('关闭按钮详情:', { testId, classes });

            // 尝试多种点击方式
            if (button.click) {
                button.click();
            } else {
                // 创建点击事件
                const clickEvent = new MouseEvent('click', {
                    bubbles: true,
                    cancelable: true,
                    view: window
                });
                button.dispatchEvent(clickEvent);
            }

            this.closedCount++;
            console.log(`已关闭 ${this.closedCount} 个目标弹窗`);

        } catch (error) {
            console.error('点击关闭按钮时出错:', error);
        }
    }

    setupPageChangeListeners() {
        // 监听DOM变化，确保新加载的内容也能被扫描
        if (typeof MutationObserver !== 'undefined') {
            this.mutationObserver = new MutationObserver((mutations) => {
                if (this.isDestroyed) return;

                let hasNewNodes = false;
                mutations.forEach(mutation => {
                    if (mutation.addedNodes.length > 0) {
                        hasNewNodes = true;
                    }
                });

                if (hasNewNodes) {
                    // 延迟一点执行，让新节点完全加载
                    setTimeout(() => {
                        if (!this.isDestroyed) {
                            this.scanAndClosePopups();
                        }
                    }, 100);
                }
            });

            this.mutationObserver.observe(document.body, {
                childList: true,
                subtree: true
            });
        }

        // 监听URL变化（SPA应用）
        let lastUrl = location.href;
        new MutationObserver(() => {
            const url = location.href;
            if (url !== lastUrl) {
                lastUrl = url;
                console.log('页面URL变化，重新启动扫描');
                setTimeout(() => {
                    if (!this.isDestroyed) {
                        this.startScanning();
                    }
                }, 1000);
            }
        }).observe(document, { subtree: true, childList: true });
    }

    setupVisibilityChangeListener() {
        document.addEventListener('visibilitychange', () => {
            if (this.isDestroyed) return;

            if (document.visibilityState === 'visible') {
                console.log('页面重新可见，重启扫描');
                setTimeout(() => {
                    if (!this.isDestroyed) {
                        this.startScanning();
                    }
                }, 500);
            }
        });

        // 监听窗口焦点变化
        window.addEventListener('focus', () => {
            if (this.isDestroyed) return;
            console.log('窗口重新获得焦点，确保扫描正常');
            setTimeout(() => {
                if (!this.isDestroyed && !this.scanInterval) {
                    this.startScanning();
                }
            }, 300);
        });
    }

    destroy() {
        this.isDestroyed = true;
        this.stopScanning();

        if (this.mutationObserver) {
            this.mutationObserver.disconnect();
        }

        console.log('吴已销毁');
    }
}

// 创建实例
const adPopupKiller = new AdPopupKiller();

// 页面卸载时清理
window.addEventListener('beforeunload', () => {
    adPopupKiller.destroy();
});
