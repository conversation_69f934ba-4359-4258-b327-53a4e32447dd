// 吴 - 内容脚本
class AdPopupKiller {
    constructor() {
        this.scanInterval = null;
        this.closedCount = 0;
        
        // 支持多种弹窗的关闭按钮选择器
        this.closeButtonSelectors = [
            // 原有的弹窗选择器（5-118-0版本）
            'svg[data-testid="beast-core-modal-icon-close"]',
            '.MDL_headerCloseIcon_5-118-0',
            '.ICN_outerWrapper_5-118-0',
            '.ICN_svgIcon_5-118-0',
            // 新增：第一个特定弹窗选择器
            'svg[data-testid="beast-core-icon-close"]',
            '.modal-content_closeIcon__2C8Fb',
            '.modal-content_closeIcon__Gw9DZ',
            // 新增：第二个特定弹窗选择器（5-119-0版本）
            '.MDL_headerCloseIcon_5-119-0',
            '.ICN_outerWrapper_5-119-0',
            '.ICN_svgIcon_5-119-0',
            // 组合选择器 - 更精准匹配
            'svg[data-testid="beast-core-icon-close"].ICN_outerWrapper_5-119-0',
            'svg[data-testid="beast-core-icon-close"].modal-content_closeIcon__2C8Fb',
            'svg[data-testid="beast-core-icon-close"].modal-content_closeIcon__Gw9DZ',
            'svg[data-testid="beast-core-modal-icon-close"].ICN_outerWrapper_5-119-0',
            'svg[data-testid="beast-core-modal-icon-close"].MDL_headerCloseIcon_5-119-0'
        ];

        // 需要排除的元素选择器（不要点击这些）
        this.excludeSelectors = [
            // 原有版本的排除选择器
            '.MDL_iconWrapper_5-118-0',
            'div.MDL_iconWrapper_5-118-0',
            // 新版本的排除选择器
            '.MDL_iconWrapper_5-119-0',
            'div.MDL_iconWrapper_5-119-0',
            // 通用排除选择器
            '[class*="MDL_iconWrapper"]',
            // 排除可能的父容器，避免误点击
            '[class*="modal-content"]',
            '.modal-content'
        ];
        
        // 特定弹窗容器选择器（更严格的匹配）
        this.popupContainerSelectors = [
            // 只匹配包含特定类名模式的容器
            '[class*="MDL_"]',
            '[class*="modal"]',
            '[role="dialog"]',
            '[role="alertdialog"]'
        ];
        
        this.init();
    }
    
    init() {
        console.log('吴已启动');
        this.startScanning();
    }
    
    startScanning() {
        if (this.scanInterval) {
            clearInterval(this.scanInterval);
        }
        
        this.scanInterval = setInterval(() => {
            this.scanAndClosePopups();
        }, 500); // 每0.5秒扫描一次
    }
    
    scanAndClosePopups() {
        try {
            // 查找并关闭弹窗
            const closeButtons = this.findCloseButtons();
            
            closeButtons.forEach(button => {
                if (this.isVisibleAndClickable(button)) {
                    this.clickCloseButton(button);
                }
            });
            
        } catch (error) {
            console.error('扫描弹窗时出错:', error);
        }
    }
    
    findCloseButtons() {
        const buttons = [];

        // 使用特定选择器查找关闭按钮
        this.closeButtonSelectors.forEach(selector => {
            try {
                const elements = document.querySelectorAll(selector);
                elements.forEach(element => {
                    // 额外验证：确保是你提供的特定SVG元素
                    if (this.isTargetCloseButton(element) && !buttons.includes(element)) {
                        buttons.push(element);
                    }
                });
            } catch (e) {
                // 忽略无效的选择器
            }
        });

        return buttons;
    }

    isTargetCloseButton(element) {
        // 首先检查是否是需要排除的元素
        if (this.isExcludedElement(element)) {
            console.log('排除的元素，不点击:', element.className);
            return false;
        }

        // 验证是否是目标关闭按钮
        if (element.tagName === 'svg') {
            // 检查SVG元素的特征
            const testId = element.getAttribute('data-testid');
            const viewBox = element.getAttribute('viewBox');
            const classes = element.className.baseVal || element.className;

            // 检查原有弹窗类型（5-118-0版本）
            const isOriginalPopup = testId === 'beast-core-modal-icon-close' &&
                                   viewBox === '0 0 1024 1024' &&
                                   (classes.includes('ICN_outerWrapper_5-118-0') ||
                                    classes.includes('MDL_headerCloseIcon_5-118-0') ||
                                    classes.includes('ICN_svgIcon_5-118-0'));

            // 检查第一个新弹窗类型
            const isFirstNewPopup = testId === 'beast-core-icon-close' &&
                                   viewBox === '0 0 1024 1024' &&
                                   (classes.includes('ICN_outerWrapper_5-119-0') ||
                                    classes.includes('ICN_svgIcon_5-119-0') ||
                                    classes.includes('modal-content_closeIcon__2C8Fb') ||
                                    classes.includes('modal-content_closeIcon__Gw9DZ'));

            // 检查第二个新弹窗类型（beast-core-modal-icon-close但5-119-0版本）
            const isSecondNewPopup = testId === 'beast-core-modal-icon-close' &&
                                    viewBox === '0 0 1024 1024' &&
                                    (classes.includes('ICN_outerWrapper_5-119-0') ||
                                     classes.includes('MDL_headerCloseIcon_5-119-0') ||
                                     classes.includes('ICN_svgIcon_5-119-0'));

            const isValidSvg = isOriginalPopup || isFirstNewPopup || isSecondNewPopup;

            // 额外检查：确保SVG不在排除的容器内
            if (isValidSvg) {
                // 检查是否在原有版本的排除容器内
                const oldParentContainer = element.closest('.MDL_iconWrapper_5-118-0');
                // 检查是否在新版本的排除容器内
                const newParentContainer = element.closest('.MDL_iconWrapper_5-119-0');

                if (oldParentContainer || newParentContainer) {
                    console.log('SVG在排除的容器内，不点击');
                    return false;
                }
                return true;
            }
            return false;
        }

        // 如果是其他元素，检查是否包含目标SVG
        const originalTargetSvg = element.querySelector('svg[data-testid="beast-core-modal-icon-close"]');
        const newTargetSvg = element.querySelector('svg[data-testid="beast-core-icon-close"]');

        if (originalTargetSvg || newTargetSvg) {
            const targetSvg = originalTargetSvg || newTargetSvg;
            // 确保找到的SVG不在排除的容器内
            const oldParentContainer = targetSvg.closest('.MDL_iconWrapper_5-118-0');
            const newParentContainer = targetSvg.closest('.MDL_iconWrapper_5-119-0');

            if (oldParentContainer || newParentContainer) {
                console.log('找到的SVG在排除的容器内，不点击');
                return false;
            }
            return true;
        }

        return false;
    }

    isExcludedElement(element) {
        // 检查元素本身是否匹配排除选择器
        for (const selector of this.excludeSelectors) {
            try {
                if (element.matches && element.matches(selector)) {
                    return true;
                }
            } catch (e) {
                // 忽略选择器错误
            }
        }

        // 检查元素是否在排除的容器内
        for (const selector of this.excludeSelectors) {
            try {
                if (element.closest && element.closest(selector)) {
                    return true;
                }
            } catch (e) {
                // 忽略选择器错误
            }
        }

        return false;
    }
    
    isVisibleAndClickable(element) {
        if (!element) return false;
        
        // 检查元素是否可见
        const style = window.getComputedStyle(element);
        if (style.display === 'none' || 
            style.visibility === 'hidden' || 
            style.opacity === '0') {
            return false;
        }
        
        // 检查元素是否在视口内
        const rect = element.getBoundingClientRect();
        if (rect.width === 0 || rect.height === 0) {
            return false;
        }
        
        // 检查是否在弹窗容器内
        return this.isInPopupContainer(element);
    }
    
    isInPopupContainer(element) {
        // 检查元素是否在特定的弹窗容器内
        let parent = element.parentElement;
        while (parent) {
            const className = parent.className || '';

            // 更严格的检查：必须包含特定的类名模式
            if (className.includes && (
                className.includes('MDL_') ||
                className.includes('modal') ||
                parent.getAttribute('role') === 'dialog' ||
                parent.getAttribute('role') === 'alertdialog'
            )) {
                // 额外检查：确保容器有较高的z-index（弹窗特征）
                const style = window.getComputedStyle(parent);
                const zIndex = parseInt(style.zIndex);
                if (zIndex > 100) {
                    return true;
                }
            }

            parent = parent.parentElement;
        }

        return false;
    }
    
    clickCloseButton(button) {
        try {
            console.log('发现目标弹窗关闭按钮，正在点击...', button);

            // 记录关闭的弹窗信息（用于调试）
            const testId = button.getAttribute('data-testid');
            const classes = button.className.baseVal || button.className;
            console.log('关闭按钮详情:', { testId, classes });

            // 尝试多种点击方式
            if (button.click) {
                button.click();
            } else {
                // 创建点击事件
                const clickEvent = new MouseEvent('click', {
                    bubbles: true,
                    cancelable: true,
                    view: window
                });
                button.dispatchEvent(clickEvent);
            }

            this.closedCount++;
            console.log(`已关闭 ${this.closedCount} 个目标弹窗`);

        } catch (error) {
            console.error('点击关闭按钮时出错:', error);
        }
    }

    destroy() {
        if (this.scanInterval) {
            clearInterval(this.scanInterval);
        }
    }
}

// 创建实例
const adPopupKiller = new AdPopupKiller();

// 页面卸载时清理
window.addEventListener('beforeunload', () => {
    adPopupKiller.destroy();
});
