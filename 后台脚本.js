// 吴 - 后台脚本
class BackgroundService {
    constructor() {
        this.init();
    }
    
    init() {
        console.log('吴后台服务已启动');
        
        // 监听插件安装
        chrome.runtime.onInstalled.addListener((details) => {
            if (details.reason === 'install') {
                console.log('吴插件已安装');
                this.showWelcomeNotification();
            }
        });

        // 监听标签页更新
        chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
            if (changeInfo.status === 'complete' && tab.url) {
                this.injectContentScript(tabId);
            }
        });

        // 监听标签页激活
        chrome.tabs.onActivated.addListener((activeInfo) => {
            this.ensureContentScriptActive(activeInfo.tabId);
        });

        // 监听窗口焦点变化
        chrome.windows.onFocusChanged.addListener((windowId) => {
            if (windowId !== chrome.windows.WINDOW_ID_NONE) {
                this.ensureAllTabsActive(windowId);
            }
        });
    }

    async injectContentScript(tabId) {
        try {
            // 检查是否可以注入脚本
            const tab = await chrome.tabs.get(tabId);
            if (!tab.url || tab.url.startsWith('chrome://') || tab.url.startsWith('chrome-extension://') || tab.url.startsWith('moz-extension://')) {
                return;
            }

            console.log('注入内容脚本到标签页:', tabId, tab.url);

            // 注入内容脚本（如果还没有注入）
            await chrome.scripting.executeScript({
                target: {tabId: tabId},
                files: ['内容脚本.js']
            });

        } catch (error) {
            // 忽略注入失败的情况（可能是权限问题）
            console.log('无法注入内容脚本到标签页:', tabId, error.message);
        }
    }

    async ensureContentScriptActive(tabId) {
        try {
            const tab = await chrome.tabs.get(tabId);
            if (!tab.url || tab.url.startsWith('chrome://') || tab.url.startsWith('chrome-extension://')) {
                return;
            }

            // 检查内容脚本是否活跃，如果不活跃则重新注入
            setTimeout(() => {
                this.injectContentScript(tabId);
            }, 500);

        } catch (error) {
            console.log('检查标签页状态失败:', tabId, error.message);
        }
    }

    async ensureAllTabsActive(windowId) {
        try {
            const tabs = await chrome.tabs.query({windowId: windowId});
            tabs.forEach(tab => {
                if (tab.url && !tab.url.startsWith('chrome://') && !tab.url.startsWith('chrome-extension://')) {
                    setTimeout(() => {
                        this.injectContentScript(tab.id);
                    }, 1000);
                }
            });
        } catch (error) {
            console.log('确保所有标签页活跃失败:', error.message);
        }
    }

    showWelcomeNotification() {
        // 可以在这里添加欢迎通知逻辑
        console.log('欢迎使用吴！');
    }
}

// 创建后台服务实例
const backgroundService = new BackgroundService();
